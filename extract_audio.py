#!/usr/bin/env python3
import os
import sys
import speech_recognition as sr
from pydub import AudioSegment
from pydub.silence import split_on_silence

def transcribe_audio(path, use_sphinx=True):
    """
    Transcribe audio file to text using <PERSON>phinx (offline) or Google's speech recognition API
    
    Args:
        path (str): Path to the audio file
        use_sphinx (bool): Whether to use Sphinx (offline) recognition
        
    Returns:
        str: Transcribed text
    """
    # Initialize recognizer
    r = sr.Recognizer()
    
    # Load audio file
    print(f"Loading audio file: {path}")
    try:
        with sr.AudioFile(path) as source:
            # Read the entire audio file
            audio_data = r.record(source)
            print("Audio file loaded successfully")
            
            # Recognize speech using Sphinx or Google
            print("Transcribing audio...")
            if use_sphinx:
                try:
                    print("Using Sphinx (offline) recognition...")
                    text = r.recognize_sphinx(audio_data)
                    return text
                except sr.UnknownValueError:
                    print("Sphinx could not understand audio, falling back to Google...")
                except sr.RequestError as e:
                    print(f"Sphinx error; {e}, falling back to Google...")
            
            # Fall back to Google if <PERSON><PERSON><PERSON> fails or is not requested
            text = r.recognize_google(audio_data)
            return text
    except Exception as e:
        print(f"Error transcribing audio: {str(e)}")
        return ""

def convert_mp3_to_wav(mp3_path, wav_path):
    """
    Convert MP3 file to WAV format (required for speech_recognition)

    Args:
        mp3_path (str): Path to the MP3 file
        wav_path (str): Path to save the WAV file
    """
    try:
        print(f"Converting MP3 to WAV: {mp3_path} -> {wav_path}")
        sound = AudioSegment.from_mp3(mp3_path)
        sound.export(wav_path, format="wav")
        print("Conversion successful")
        return True
    except Exception as e:
        print(f"Error converting MP3 to WAV: {str(e)}")
        return False

def match_target_amplitude(sound, target_dBFS):
    """
    Normalize audio to target amplitude

    Args:
        sound (AudioSegment): Audio segment to normalize
        target_dBFS (float): Target amplitude in dBFS (e.g., -20.0)

    Returns:
        AudioSegment: Normalized audio segment
    """
    change_in_dBFS = target_dBFS - sound.dBFS
    return sound.apply_gain(change_in_dBFS)

def transcribe_large_audio(path, output_path=None, use_sphinx=False):
    """
    Transcribe large audio file by splitting it into chunks
    
    Args:
        path (str): Path to the audio file
        output_path (str, optional): Path to save the transcribed text
        use_sphinx (bool): Whether to use Sphinx (offline) recognition
        
    Returns:
        str: Transcribed text
    """
    # Check if file exists
    if not os.path.exists(path):
        print(f"Error: Audio file '{path}' does not exist.")
        return ""
    
    # Convert MP3 to WAV if needed
    if path.lower().endswith('.mp3'):
        wav_path = path.replace('.mp3', '.wav')
        if not convert_mp3_to_wav(path, wav_path):
            return ""
        path = wav_path
    
    # Initialize recognizer
    r = sr.Recognizer()
    
    # Adjust the recognizer sensitivity to ambient noise
    # This can help with recognition accuracy
    print(f"Processing audio file: {path}")
    with sr.AudioFile(path) as source:
        r.adjust_for_ambient_noise(source, duration=0.5)
    
    # Split audio into chunks and transcribe
    sound = AudioSegment.from_file(path)
    
    # Adjust these parameters for better chunk splitting
    chunks = split_on_silence(
        sound,
        min_silence_len=700,  # increased from 500ms to 700ms
        silence_thresh=sound.dBFS-12,  # adjusted threshold (less sensitive)
        keep_silence=700  # increased from 500ms to 700ms
    )
    
    print(f"Audio split into {len(chunks)} chunks")
    
    whole_text = ""
    failed_chunks = []
    
    for i, chunk in enumerate(chunks):
        # Export chunk and transcribe
        chunk_filename = f"chunk{i}.wav"
        
        # Normalize the audio chunk (adjust volume)
        normalized_chunk = match_target_amplitude(chunk, -20.0)
        normalized_chunk.export(chunk_filename, format="wav")
        
        try:
            with sr.AudioFile(chunk_filename) as source:
                audio_data = r.record(source)
                
                # Try with Sphinx first if requested
                if use_sphinx:
                    try:
                        text = r.recognize_sphinx(audio_data)
                        whole_text += text + " "
                        print(f"Chunk {i+1}/{len(chunks)} transcribed with Sphinx")
                    except (sr.UnknownValueError, sr.RequestError) as e:
                        print(f"Sphinx failed on chunk {i+1}, trying Google: {str(e)}")
                        try:
                            text = r.recognize_google(audio_data)
                            whole_text += text + " "
                            print(f"Chunk {i+1}/{len(chunks)} transcribed with Google")
                        except:
                            print(f"Could not understand audio in chunk {i+1}")
                            failed_chunks.append(i+1)
                else:
                    # Try with Google first
                    try:
                        text = r.recognize_google(audio_data)
                        whole_text += text + " "
                        print(f"Chunk {i+1}/{len(chunks)} transcribed with Google")
                    except sr.UnknownValueError:
                        # If Google fails, try with Sphinx (offline recognition)
                        try:
                            print(f"Retrying chunk {i+1} with Sphinx...")
                            text = r.recognize_sphinx(audio_data)
                            whole_text += text + " "
                            print(f"Chunk {i+1}/{len(chunks)} transcribed with Sphinx")
                        except:
                            print(f"Could not understand audio in chunk {i+1}")
                            failed_chunks.append(i+1)
        except Exception as e:
            print(f"Error processing chunk {i+1}: {str(e)}")
            failed_chunks.append(i+1)
        
        # Remove temporary file
        if os.path.exists(chunk_filename):
            os.remove(chunk_filename)
    
    # Report on failed chunks
    if failed_chunks:
        print(f"Failed to transcribe {len(failed_chunks)} chunks out of {len(chunks)}")
        print(f"Failed chunk numbers: {failed_chunks}")
    
    # Save to file if output path is provided
    if output_path:
        with open(output_path, 'w', encoding='utf-8') as output_file:
            output_file.write(whole_text)
        print(f"Transcription completed. Saved to '{output_path}'")
    
    return whole_text

if __name__ == "__main__":
    import argparse
    
    # Create argument parser
    parser = argparse.ArgumentParser(description='Extract text from an audio file.')
    parser.add_argument('audio_path', help='Path to the audio file')
    parser.add_argument('output_path', help='Path to save the extracted text')
    parser.add_argument('--large', action='store_true', 
                        help='Process as a large audio file (split into chunks)')
    parser.add_argument('--sphinx', action='store_true',
                        help='Use Sphinx (offline) speech recognition instead of Google')
    
    # Parse arguments
    args = parser.parse_args()
    
    # Extract text from the audio
    if args.large:
        text = transcribe_large_audio(args.audio_path, args.output_path)
    else:
        # For smaller files, convert if needed and transcribe directly
        path = args.audio_path
        if path.lower().endswith('.mp3'):
            wav_path = path.replace('.mp3', '.wav')
            if convert_mp3_to_wav(path, wav_path):
                path = wav_path
        
        text = transcribe_audio(path, use_sphinx=args.sphinx)
        
        # Save to file
        if text and args.output_path:
            with open(args.output_path, 'w', encoding='utf-8') as output_file:
                output_file.write(text)
            print(f"Transcription completed. Saved to '{args.output_path}'")
    
    # Exit with appropriate status code
    sys.exit(0 if text else 1)
