#!/usr/bin/env python3
import os
import sys
import json
import argparse
import torch
import whisperx
from datetime import datetime

def transcribe_with_whisperx(audio_path, output_dir=None, model_size="medium", 
                            diarize=True, min_speakers=1, max_speakers=8, 
                            language="en", compute_type="float16", hf_token="*************************************"):
    """
    Transcribe audio using WhisperX (Whisper + pyannote diarization)
    
    Args:
        audio_path (str): Path to the audio file
        output_dir (str, optional): Directory to save output files
        model_size (str): Whisper model size (tiny, base, small, medium, large, large-v2)
        diarize (bool): Whether to perform speaker diarization
        min_speakers (int): Minimum number of speakers to detect
        max_speakers (int): Maximum number of speakers to detect
        language (str): Language code (e.g., "en" for English)
        compute_type (str): Computation type (float16, float32, int8)
        hf_token (str): Hugging<PERSON><PERSON> token for accessing diarization models
        
    Returns:
        dict: Transcription results with segments and speaker information
    """
    # Check if file exists
    if not os.path.exists(audio_path):
        print(f"Error: Audio file '{audio_path}' does not exist.")
        return None
    
    # Create output directory if it doesn't exist
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Determine device
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")
    
    try:
        # Step 1: Load model and transcribe
        print(f"Loading Whisper model ({model_size})...")
        model = whisperx.load_model(model_size, device, compute_type=compute_type, language=language)
        
        print(f"Transcribing audio: {audio_path}")
        result = model.transcribe(audio_path, batch_size=16)
        print("Initial transcription complete")
        
        # Step 2: Align words with timestamps
        print("Aligning words with timestamps...")
        model_a, metadata = whisperx.load_align_model(language_code=language, device=device)
        result = whisperx.align(result["segments"], model_a, metadata, audio_path, device)
        
        # Step 3: Diarize if requested
        if diarize:
            if not hf_token:
                print("Warning: HuggingFace token not provided. Diarization may fail.")
                print("You can get a token at https://huggingface.co/settings/tokens")
            
            print("Performing speaker diarization...")
            diarize_model = whisperx.DiarizationPipeline(use_auth_token=hf_token, device=device)
            
            diarize_segments = diarize_model(
                audio_path,
                min_speakers=min_speakers,
                max_speakers=max_speakers
            )
            
            result = whisperx.assign_word_speakers(diarize_segments, result)
            print("Speaker diarization complete")
        
        # Step 4: Save results
        if output_dir:
            base_filename = os.path.splitext(os.path.basename(audio_path))[0]
            
            # Save JSON
            json_path = os.path.join(output_dir, f"{base_filename}.json")
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2)
            print(f"JSON output saved to: {json_path}")
            
            # Save SRT
            srt_path = os.path.join(output_dir, f"{base_filename}.srt")
            write_srt(result, srt_path)
            print(f"SRT output saved to: {srt_path}")
            
            # Save plain text
            text_path = os.path.join(output_dir, f"{base_filename}.txt")
            write_text(result, text_path)
            print(f"Text output saved to: {text_path}")
        
        return result
    
    except Exception as e:
        print(f"Error transcribing audio: {str(e)}")
        return None
    finally:
        # Clean up GPU memory
        if device == "cuda":
            torch.cuda.empty_cache()

def write_srt(result, output_path):
    """Write transcription result to SRT file"""
    with open(output_path, 'w', encoding='utf-8') as f:
        for i, segment in enumerate(result["segments"], 1):
            start_time = format_timestamp(segment["start"])
            end_time = format_timestamp(segment["end"])
            
            # Add speaker label if available
            text = segment["text"].strip()
            if "speaker" in segment:
                text = f"[{segment['speaker']}] {text}"
            
            f.write(f"{i}\n")
            f.write(f"{start_time} --> {end_time}\n")
            f.write(f"{text}\n\n")

def write_text(result, output_path):
    """Write transcription result to plain text file"""
    with open(output_path, 'w', encoding='utf-8') as f:
        current_speaker = None
        for segment in result["segments"]:
            speaker = segment.get("speaker", "Unknown")
            text = segment["text"].strip()
            
            # Only add speaker label when speaker changes
            if speaker != current_speaker:
                f.write(f"\n[{speaker}]: ")
                current_speaker = speaker
            else:
                f.write(" ")  # Continue same speaker's text
            
            f.write(text)
        f.write("\n")

def format_timestamp(seconds):
    """Convert seconds to SRT timestamp format (HH:MM:SS,mmm)"""
    hours = int(seconds / 3600)
    minutes = int((seconds % 3600) / 60)
    seconds = seconds % 60
    milliseconds = int((seconds - int(seconds)) * 1000)
    
    return f"{hours:02d}:{minutes:02d}:{int(seconds):02d},{milliseconds:03d}"

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Extract text from audio using WhisperX.')
    parser.add_argument('audio_path', help='Path to the audio file')
    parser.add_argument('--output_dir', default='output', help='Directory to save output files')
    parser.add_argument('--model', default='medium', choices=['tiny', 'base', 'small', 'medium', 'large', 'large-v2'],
                        help='Whisper model size')
    parser.add_argument('--diarize', action='store_true', help='Perform speaker diarization')
    parser.add_argument('--min_speakers', type=int, default=1, help='Minimum number of speakers')
    parser.add_argument('--max_speakers', type=int, default=2, help='Maximum number of speakers')
    parser.add_argument('--language', default='en', help='Language code (e.g., "en" for English)')
    parser.add_argument('--compute_type', default='float16', choices=['float16', 'float32', 'int8'],
                        help='Computation type')
    parser.add_argument('--hf_token', help='HuggingFace token for accessing diarization models')
    
    args = parser.parse_args()
    
    # Get HF token from environment if not provided
    hf_token = args.hf_token or os.environ.get('HF_TOKEN')
    
    # Run transcription
    result = transcribe_with_whisperx(
        args.audio_path,
        args.output_dir,
        args.model,
        args.diarize,
        args.min_speakers,
        args.max_speakers,
        args.language,
        args.compute_type,
        hf_token
    )
    
    # Exit with appropriate status code
    sys.exit(0 if result else 1)