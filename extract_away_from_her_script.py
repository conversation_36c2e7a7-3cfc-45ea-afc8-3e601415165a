#!/usr/bin/env python3
"""
Script to extract the complete "Away from Her" screenplay from scripts.com
"""

import requests
from bs4 import BeautifulSoup
import re
import time
import sys

def clean_script_text(text):
    """Clean and format the script text"""
    # Remove extra whitespace and normalize line breaks
    text = re.sub(r'\s+', ' ', text)
    text = re.sub(r'\s*\n\s*', '\n', text)
    
    # Remove hyperlinks and definitions
    text = re.sub(r'https://www\.definitions\.net/definition/\w+', '', text)
    text = re.sub(r'https://www\.abbreviations\.com/\w+', '', text)
    
    # Clean up common formatting issues
    text = text.replace('Rate this script:', '')
    text = text.replace('/ 1 vote', '')
    text = text.replace('« Prev', '')
    text = text.replace('Next »', '')
    
    # Remove page navigation elements
    text = re.sub(r'Page #\d+', '', text)
    
    return text.strip()

def extract_script_content(html):
    """Extract the main script content from HTML"""
    soup = BeautifulSoup(html, 'html.parser')
    
    # Find the main script content - it's usually in blockquotes
    script_content = ""
    
    # Look for blockquotes containing the script
    blockquotes = soup.find_all('blockquote')
    for bq in blockquotes:
        script_content += bq.get_text() + "\n"
    
    # If no blockquotes, look for other content containers
    if not script_content.strip():
        # Try to find content between navigation elements
        content_divs = soup.find_all('div', class_='content')
        for div in content_divs:
            script_content += div.get_text() + "\n"
    
    return clean_script_text(script_content)

def fetch_script_page(page_num=None):
    """Fetch a single page of the script"""
    if page_num is None or page_num == 1:
        url = "https://www.scripts.com/script/away_from_her_995"
    else:
        url = f"https://www.scripts.com/script/away_from_her_995/{page_num}"
    
    try:
        print(f"Fetching page {page_num if page_num else 1}...")
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        return response.text
    except requests.RequestException as e:
        print(f"Error fetching page {page_num}: {e}")
        return None

def extract_complete_script():
    """Extract the complete script from all pages"""
    complete_script = ""
    page_num = 1
    max_pages = 50  # Safety limit
    
    while page_num <= max_pages:
        html = fetch_script_page(page_num if page_num > 1 else None)
        
        if html is None:
            print(f"Failed to fetch page {page_num}")
            break
            
        content = extract_script_content(html)
        
        if not content.strip():
            print(f"No content found on page {page_num}, stopping.")
            break
            
        # Check if this is a duplicate or end of script
        if content in complete_script:
            print(f"Duplicate content detected on page {page_num}, stopping.")
            break
            
        complete_script += f"\n--- PAGE {page_num} ---\n"
        complete_script += content + "\n"
        
        # Check if there's a "Next" link indicating more pages
        if "Next »" not in html:
            print(f"No more pages after page {page_num}")
            break
            
        page_num += 1
        time.sleep(1)  # Be respectful to the server
    
    return complete_script

def main():
    print("Extracting 'Away from Her' screenplay...")
    
    script_text = extract_complete_script()
    
    if script_text:
        # Save to file
        output_file = "away_from_her_script.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("AWAY FROM HER\n")
            f.write("Written and Directed by Sarah Polley\n")
            f.write("Based on the short story 'The Bear Came Over the Mountain' by Alice Munro\n")
            f.write("=" * 80 + "\n\n")
            f.write(script_text)
        
        print(f"Script extracted and saved to {output_file}")
        print(f"Total length: {len(script_text)} characters")
    else:
        print("Failed to extract script content")
        sys.exit(1)

if __name__ == "__main__":
    main()
